import pandas as pd
import re
import os
import sys
import tkinter as tk
from tkinter import Toplevel, Entry, Label, Button, messagebox
from tkinter import filedialog

# ==============================================================================
# ČÁST 0: Dialogové okno pro zadání chybějících dat
# ==============================================================================

class InputDialog(Toplevel):
    def __init__(self, parent, title=None, customer_no_label="Číslo zákazníka:"):
        super().__init__(parent)
        self.parent = parent
        self.transient(parent)
        if title: self.title(title)
        self.result = None
        self.body = tk.Frame(self)
        self.initial_focus = self.body_contents(self.body, customer_no_label)
        self.body.pack(padx=5, pady=5)
        self.buttonbox()
        self.grab_set()
        if not self.initial_focus: self.initial_focus = self
        self.protocol("WM_DELETE_WINDOW", self.cancel)
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        self.initial_focus.focus_set()
        self.wait_window(self)

    def body_contents(self, master, customer_no_label):
        Label(master, text="Datum dodání (DD.MM.RRRR):").grid(row=0, sticky="w")
        Label(master, text=customer_no_label).grid(row=1, sticky="w")
        Label(master, text="Číslo obj. zákazníka (PO):").grid(row=2, sticky="w")
        self.entry_date = Entry(master)
        self.entry_customer = Entry(master)
        self.entry_po = Entry(master)
        self.entry_date.insert(0, "DD.MM.RRRR")
        self.entry_customer.insert(0, "Číslo")
        self.entry_po.insert(0, "PO číslo")
        self.entry_date.grid(row=0, column=1)
        self.entry_customer.grid(row=1, column=1)
        self.entry_po.grid(row=2, column=1)
        return self.entry_date

    def buttonbox(self):
        box = tk.Frame(self)
        Button(box, text="OK", width=10, command=self.ok, default=tk.ACTIVE).pack(side=tk.LEFT, padx=5, pady=5)
        Button(box, text="Zrušit", width=10, command=self.cancel).pack(side=tk.LEFT, padx=5, pady=5)
        self.bind("<Return>", self.ok)
        self.bind("<Escape>", self.cancel)
        box.pack()

    def ok(self, event=None):
        self.result = (self.entry_date.get(), self.entry_customer.get(), self.entry_po.get())
        self.withdraw()
        self.update_idletasks()
        self.parent.focus_set()
        self.destroy()

    def cancel(self, event=None):
        self.parent.focus_set()
        self.destroy()

# ==============================================================================
# ČÁST 1: ZPRACOVÁNÍ DAT (JÁDRO PROGRAMU)
# ==============================================================================

def load_data(path):
    """Načte data z CSV nebo Excel souboru."""
    if path.endswith('.csv'):
        return pd.read_csv(path, sep=';', encoding='windows-1250')
    elif path.endswith(('.xlsx', '.xls')):
        return pd.read_excel(path)
    else:
        raise ValueError("Nepodporovaný formát souboru. Použijte .csv, .xlsx nebo .xls")

def process_labara_order(order_path, catalog_path):
    """Zpracuje objednávku Labara pomocí spolehlivého párování podle unikátního kódu."""
    print(f"--- Zpracovávám objednávku Labara: {os.path.basename(order_path)} ---")
    try:
        order_items_df = load_data(order_path)
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
    except Exception as e:
        raise ValueError(f"Chyba při načítání dat. Zkontrolujte soubory.\n\nDetail chyby: {e}")

    order_items_df.rename(columns={'číslo zboží': 'cislo_zbozi', 'množství': 'mnozstvi', 'název': 'nazev'}, inplace=True)
    order_items_df = order_items_df.dropna(subset=['cislo_zbozi'])

    NAZEV_SLOUPCE_OD_ZAKAZNIKA = 'CISLO_ZBOZI_ZAKAZNIKA'
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky', NAZEV_SLOUPCE_OD_ZAKAZNIKA: 'cislo_zbozi_zakaznika'}, inplace=True)
    if 'cislo_zbozi_zakaznika' not in alde_catalog_df.columns:
        raise ValueError(f"Kritická chyba: V katalogu chybí sloupec '{NAZEV_SLOUPCE_OD_ZAKAZNIKA}'.")

    order_items_df['cislo_zbozi'] = order_items_df['cislo_zbozi'].astype(str)
    alde_catalog_df['cislo_zbozi_zakaznika'] = alde_catalog_df['cislo_zbozi_zakaznika'].astype(str)
    final_df = pd.merge(order_items_df, alde_catalog_df, left_on='cislo_zbozi', right_on='cislo_zbozi_zakaznika', how='left')

    customer_po_no = os.path.splitext(os.path.basename(order_path))[0]
    final_df['mnozstvi'] = final_df['mnozstvi'].astype(str).str.replace(' ks', '', regex=False).astype(int)

    def parse_dimensions(nazev):
        nazev = str(nazev)
        match_dims = re.search(r'-\s*([\d,\.]+)\s*[-x]\s*([\d,\.]+)', nazev, re.IGNORECASE)
        if match_dims: return match_dims.group(1).strip().replace(',', '.'), match_dims.group(2).strip().replace(',', '.')
        return None, None

    dims_data = final_df['nazev'].apply(lambda x: pd.Series(parse_dimensions(x)))
    final_df[['W', 'L']] = dims_data

    # Tyto hodnoty se pro Labara zadají ručně
    dated_input, customer_no_input, _ = ("Zadejte v okně", "Zadejte v okně", "") # Dummy values

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': customer_no_input, 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['mnozstvi'], 'DATED': dated_input, 'PRICE': 0, 'CURRENCY_CODE': 'CZK',
        'CUSTOMER_PO_NO': customer_po_no, 'W': final_df['W'], 'L': final_df['L'], 'Popis Položky': final_df['Popis Položky']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

def process_impa_order(order_path, catalog_path):
    """Zpracuje objednávku IMPA."""
    print(f"--- Zpracovávám objednávku IMPA: {os.path.basename(order_path)} ---")
    try:
        order_items_df = load_data(order_path)
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
    except Exception as e:
        raise ValueError(f"Chyba při načítání dat. Zkontrolujte soubory.\n\nDetail chyby: {e}")
        
    order_items_df.rename(columns={'Material': 'material', 'Hrubka': 'hrubka', 'Sirka': 'W', 'Dlzka': 'L', 'Pocet kusov': 'QTY'}, inplace=True)
    
    def create_impa_key(row):
        material = str(row['material']).replace("EN AW ", "ENAW").replace(" ", "")
        hrubka = float(row['hrubka'])
        return f"{material} {hrubka:.2f}".replace('.', ',')

    order_items_df['search_key'] = order_items_df.apply(create_impa_key, axis=1)

    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky'}, inplace=True)
    alde_catalog_df['search_key'] = alde_catalog_df['Popis Položky'].str.strip()
    order_items_df['search_key'] = order_items_df['search_key'].str.strip()

    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'search_key']], on='search_key', how='left')
    
    # Tyto hodnoty se pro Impa zadají ručně
    dated_input, customer_no_input, po_input = ("Zadejte v okně", "Zadejte v okně", "Zadejte v okně") # Dummy values

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': customer_no_input, 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['QTY'], 'DATED': dated_input, 'PRICE': 0, 'CURRENCY_CODE': 'CZK',
        'CUSTOMER_PO_NO': po_input, 'W': final_df['W'], 'L': final_df['L'], 'Popis Položky': final_df['Popis Položky']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)
    return output_df

def process_descon_order(order_path, catalog_path):
    """Zpracuje objednávku Descon."""
    print(f"--- Zpracovávám objednávku Descon: {os.path.basename(order_path)} ---")
    try:
        order_items_df = pd.read_excel(order_path, sheet_name=1, header=0, skipfooter=3)
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
    except Exception as e:
        raise ValueError(f"Chyba při načítání dat pro Descon.\n\nDetail chyby: {e}")

    order_items_df = order_items_df[order_items_df['CisloDilu'].str.contains('_', na=False)]
    typ_column_name = order_items_df.columns[3]
    date_column_name = order_items_df.columns[-1]

    def create_search_key(row):
        material = row['MaterialVyroba']; typ = row[typ_column_name]; thickness = row['Tloustka']
        base_material = ""
        if "5083" in material: base_material = "5083"
        elif "6082" in material: base_material = "ENAW6082T651"
        if "frézovaná" in str(typ): base_material += " litá frézovaná"
        elif "litá" in str(typ): base_material += " litá"
        if not base_material or pd.isna(thickness): return None
        return f"{base_material} {float(thickness):.2f}".replace('.', ',')

    order_items_df['search_key'] = order_items_df.apply(create_search_key, axis=1)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'Popis Položky'}, inplace=True)
    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'Popis Položky', 'search_key']], on='search_key', how='left')

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': '505992', 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['KS'], 'DATED': pd.to_datetime(final_df[date_column_name].iloc[0]).strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK', 'CUSTOMER_PO_NO': final_df['Zakazka'],
        'W': final_df['SirkaPrumer'], 'L': final_df['Delka'], 'Popis Položky': final_df['search_key']
    })
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_folder = os.path.join(script_dir, 'vystup')
    if not os.path.exists(output_folder): os.makedirs(output_folder)
    output_path = os.path.join(output_folder, 'vystup_descon.csv')
    output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
    return output_path

# ==============================================================================
# ČÁST 2: GRAFICKÉ ROZHRANÍ (GUI)
# ==============================================================================

def run_processing(root, process_func, title, filetypes, ask_for_data=False):
    filepath = filedialog.askopenfilename(title=title, filetypes=filetypes)
    if not filepath: return

    dated, customer_no, po_no = None, None, None
    if ask_for_data:
        dialog = InputDialog(root, "Doplňte chybějící údaje")
        if not dialog.result: return
        dated, customer_no, po_no = dialog.result

    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        catalog_path = os.path.join(script_dir, 'ALDE_katalog_položek.xlsx')
        if not os.path.exists(catalog_path):
            messagebox.showerror("Chyba souboru", "Katalogový soubor 'ALDE_katalog_položek.xlsx' nebyl nalezen.")
            return

        if process_func == process_descon_order:
             output_df = process_func(filepath, catalog_path)
             customer_po_no_for_filename = output_df['CUSTOMER_PO_NO'].iloc[0]
        else:
            output_df = process_func(filepath, catalog_path)
            output_df['DATED'] = pd.to_datetime(dated, format='%d.%m.%Y').strftime('%d.%m.%Y')
            output_df['CUSTOMER_NO'] = customer_no
            if process_func == process_impa_order:
                 output_df['CUSTOMER_PO_NO'] = po_no
            customer_po_no_for_filename = output_df['CUSTOMER_PO_NO'].iloc[0]

        output_folder = os.path.join(script_dir, 'vystup')
        if not os.path.exists(output_folder): os.makedirs(output_folder)
        output_filename = f"vystup_{customer_po_no_for_filename}.csv"
        output_path = os.path.join(output_folder, output_filename)
        output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')

        messagebox.showinfo("Hotovo", f"Zpracování dokončeno!\n\nVýstup byl uložen do:\n{output_path}")

    except Exception as e:
        messagebox.showerror("Chyba při zpracování", f"Vyskytla se neočekávaná chyba:\n\n{e}")

if __name__ == "__main__":
    root = tk.Tk()
    root.title("Nástroj pro migraci objednávek v9.0 (Labara, Descon, Impa)")
    root.geometry("450x250") # Zvětšeno pro třetí tlačítko
    root.resizable(False, False)

    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(expand=True, fill=tk.BOTH)

    label = tk.Label(main_frame, text="Vyberte typ objednávky ke zpracování:", font=("Segoe UI", 12))
    label.pack(pady=(0, 15))

    filetypes_all = [("Podporované soubory", "*.csv *.xlsx *.xls"), ("Všechny soubory", "*.*")]

    btn_labara = tk.Button(main_frame, text="Zpracovat objednávku LABARA", 
        command=lambda: run_processing(root, process_labara_order, "Vyberte objednávku LABARA", filetypes_all, ask_for_data=True), 
        width=40, height=2)
    btn_labara.pack(pady=5)
    
    btn_descon = tk.Button(main_frame, text="Zpracovat objednávku DESCON", 
        command=lambda: run_processing(root, process_descon_order, "Vyberte objednávku DESCON", [("Excel soubory", "*.xlsx")]), 
        width=40, height=2)
    btn_descon.pack(pady=5)

    btn_impa = tk.Button(main_frame, text="Zpracovat objednávku IMPA", 
        command=lambda: run_processing(root, process_impa_order, "Vyberte objednávku IMPA", filetypes_all, ask_for_data=True), 
        width=40, height=2, bg='#D4EDDA')
    btn_impa.pack(pady=5)

    root.mainloop()